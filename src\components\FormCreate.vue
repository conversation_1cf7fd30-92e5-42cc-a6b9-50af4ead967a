<template>
  <div class="form-container">
    <van-form @submit="onSubmit" validate-first>
    <van-cell v-for="(item, index) in formFields" :key="index">
      <van-field
        name="radio"
        v-if="isTeam ===3 && item.fieldName === '队伍'"
        label="是否成为队长"
        :required="item.must"
        :rules="[{required: item.must, message: '请选择是否成为队长'}]"
        >
        <template #input>
          <div class="radio-group-wrapper">
            <van-radio-group v-model="isTeamLader" direction="horizontal">
              <van-radio name="1" class="radio-option">是</van-radio>
              <van-radio name="0" class="radio-option">否</van-radio>
            </van-radio-group>
          </div>
        </template>
      </van-field>
      <!--
          isTeam 值 0：不组队，1：队长组队，2：部门组队，3：自由组队
          如果是队长组队、部门组队、或自由组队非队长时，变成队伍选择框
       -->
      <van-field
        name="select"
        v-if="item.fieldName === '队伍'&& ((isTeam === 3 && isTeamLader ==='0') || isTeam === 1 || isTeam === 2)"
        :label="item.fieldName"
        :required="item.must"
        :rules="[{required: item.must, message: `请选择队伍`}]"
        >
        <template #input>
          <div class="select-wrapper">
            <van-dropdown-menu>
              <van-dropdown-item
                v-model="formFieldsValue[item.fieldName]"
                :options="teamOptions"
                :placeholder="'请选择队伍'"
              />
            </van-dropdown-menu>
          </div>
        </template>
      </van-field>
      <van-field
        name="radio"
        v-else-if="item.fieldName === '性别' || item.fieldName === '是否携带亲属'"
        :label="item.fieldName"
        :required="item.must"
        :rules="[{required: item.must, message: `请选择${item.fieldName}`}]"
        >
        <template #input>
          <van-radio-group v-model="formFieldsValue[item.fieldName]" direction="horizontal">
            <van-radio name="1">{{item.fieldName === '性别'? "男" : '是'}}</van-radio>
            <van-radio name="2">{{item.fieldName === '性别'? "女" : '否'}}</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <!-- :disabled="item.fieldName === '姓名'" -->
      <van-field
        v-model="formFieldsValue[item.fieldName]"
        :label="item.fieldName"
        :type="item.fieldName === '电话' ? 'tel' : ''"
        v-else
        :required="item.must"
        :rules="getRules(item)"
      />
    </van-cell>

      <slot></slot>
      <div class="submit-button-container">
        <van-button
          round
          block
          type="info"
          native-type="submit"
          :disabled="!isDisabled">
          提交
        </van-button>
      </div>
    </van-form>
  </div>

</template>

<script>
export default {
  props: {
    formFields: {
      type: Array,
      require: true
    },
    formFieldsValue: {
      type: Object,
      require: true
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Array,
      default() {
        return [];
      }
    },
    isTeam: {
      type: Number,
      default: 0
    },
    team: {
      type: Object,
      default: () => {
        return {};
      }
    },
    bringRelativesNum: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      username: '',
      isTeamLader: '1', // 默认选择"是"
      selectedTeam: {}
    };
  },
  computed: {
    // 将队伍数据转换为下拉选项格式
    teamOptions() {
      if (!this.columns || this.columns.length === 0) {
        return [];
      }
      return this.columns.map(item => ({
        text: item.teamName || item.text,
        value: item.teamName || item.text,
        disabled: item.disabled || false
      }));
    }
  },
  mounted() {
    console.log('FormCreate mounted, isTeam:', this.isTeam);
    console.log('FormCreate mounted, formFields:', this.formFields);
  },
  watch: {
    team() {
      // this.isTeamLader = this.team.id ? '0' : '1';
      this.selectedTeam = this.team;
      // 如果有预选的队伍，设置表单值
      if (this.team && this.team.teamName) {
        this.formFieldsValue['队伍'] = this.team.teamName;
      }
    }
  },
  methods: {
    getRules(item) {
      const rules = [{ required: item.must, message: `请输入${item.fieldName}` }];
      if (item.fieldName === '电话' && item.must) {
        rules.push({ pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/, message: '请输入正确的电话号码' });
      }

      if (item.fieldName === '亲属人数') {
        rules.push({
          validator: val => {
            return Number(val) <= this.bringRelativesNum;
          },
          message: `亲属人数不能多余${this.bringRelativesNum}个`
        });
      }
      return rules;
    },
    onSubmit() {
      // 队长组队或部门组队时
      if (this.isTeam === 1 || this.isTeam === 2) {
        this.isTeamLader = '0';
      }
      this.$emit('submit', this.formFieldsValue, this.isTeamLader);
    },

  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

// 表单容器美化
.form-container {
  :global(.van-form) {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
  }

  // 表单项美化
  :global(.van-cell) {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 16px 20px;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(37, 99, 235, 0.02);
    }
  }

  // 字段标签美化
  :global(.van-field__label) {
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
    min-width: 80px;
    position: relative;

    // 必填标记
    &::after {
      content: '*';
      color: #f56c6c;
      margin-left: 4px;
      font-weight: bold;
    }

    // 标签内的span文字颜色
    span {
      color: #000000;
    }
  }

  // 输入框美化
  :global(.van-field__control) {
    color: #2c3e50;
    font-size: 14px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &::placeholder {
      color: #909399;
    }

    &:focus {
      background: rgba(37, 99, 235, 0.05);
      border: 1px solid rgba(37, 99, 235, 0.3);
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
    }
  }

  // 特殊处理包含单选框组的字段样式
  :global(.van-cell) {
    // 针对包含单选框组的字段
    &:has(.van-radio-group) .van-field__label,
    .van-field:has(.van-radio-group) .van-field__label {
      color: #000000 !important;
      font-weight: 500;

      span {
        color: #000000 !important;
      }

      // 必填标记也改为黑色
      &::after {
        color: #f56c6c !important;
      }
    }
  }

  // 兼容性处理 - 如果浏览器不支持:has()选择器
  :global(.van-field[name="radio"]) {
    .van-field__label {
      color: #000000 !important;
      font-weight: 500;

      span {
        color: #000000 !important;
      }

      &::after {
        color: #f56c6c !important;
      }
    }

    // 只针对单选框字段去除输入框边界
    .van-field__control {
      background: none !important;
      border: none !important;
      box-shadow: none !important;
      padding: 0 !important;
      border-radius: 0 !important;

      &:focus {
        background: none !important;
        border: none !important;
        box-shadow: none !important;
      }
    }
  }

  // 单选框组包装器
  .radio-group-wrapper {
    width: 100%;
    padding: 8px 0;

    // 单选框组样式
    :global(.van-radio-group) {
      display: flex;
      gap: 12px;
      width: 100%;

      :global(.van-radio) {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 16px;
        border-radius: 8px;
        border: 2px solid #e4e7ed;
        background: #ffffff;
        transition: all 0.3s ease;
        cursor: pointer;
        min-height: 40px;

        &:hover {
          border-color: #2563eb;
          background: rgba(37, 99, 235, 0.05);
        }

        :global(.van-radio__icon) {
          margin-right: 6px;

          :global(.van-icon) {
            width: 16px;
            height: 16px;
            border: 2px solid #dcdfe6;
            border-radius: 50%;
            background: #fff;
            transition: all 0.3s ease;
          }
        }

        :global(.van-radio__label) {
          color: #606266;
          font-weight: 500;
          font-size: 14px;
          text-align: center;
        }

        // 选中状态
        &:global(.van-radio--checked) {
          border-color: #2563eb;
          background: rgba(37, 99, 235, 0.1);

          :global(.van-radio__icon .van-icon) {
            background: #2563eb;
            border-color: #2563eb;
            color: #fff;
          }

          :global(.van-radio__label) {
            color: #2563eb;
            font-weight: 600;
          }
        }
      }
    }
  }

  // 下拉选择器包装器
  .select-wrapper {
    width: 100%;
    padding: 8px 0;

    // 下拉菜单样式
    :global(.van-dropdown-menu) {
      width: 100%;

      :global(.van-dropdown-item) {
        width: 100%;

        :global(.van-dropdown-item__title) {
          padding: 10px 16px;
          border-radius: 8px;
          border: 2px solid #e4e7ed;
          background: #ffffff;
          transition: all 0.3s ease;
          cursor: pointer;
          min-height: 40px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #606266;
          font-weight: 500;
          font-size: 14px;

          &:hover {
            border-color: #2563eb;
            background: rgba(37, 99, 235, 0.05);
          }

          // 下拉箭头
          &::after {
            content: '▼';
            font-size: 12px;
            color: #909399;
            transition: transform 0.3s ease;
          }

          // 展开状态
          &.van-dropdown-item__title--active {
            border-color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
            color: #2563eb;

            &::after {
              transform: rotate(180deg);
              color: #2563eb;
            }
          }
        }
      }
    }
  }

  // 只读字段（选择器）美化
  :global(.van-field--readonly) {
    .van-field__control {
      background: rgba(37, 99, 235, 0.05);
      border: 1px solid rgba(37, 99, 235, 0.2);
      border-radius: 8px;
      padding: 10px 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &::after {
        content: '▼';
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #909399;
        font-size: 12px;
        transition: transform 0.3s ease;
      }

      &:active {
        border-color: #2563eb;
        box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);

        &::after {
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
  }

  // 提交按钮美化
  .submit-button-container {
    padding: 24px 16px 16px;

    // 只针对表单提交按钮的样式，不影响其他页面的按钮
    .van-button {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border: none;
      border-radius: 12px;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      // 按钮光效
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(37, 99, 235, 0.4);

        &::before {
          left: 100%;
        }
      }

      &:disabled {
        background: linear-gradient(135deg, #c0c4cc 0%, #a8abb2 100%);
        box-shadow: none;
        transform: none;
        cursor: not-allowed;

        &::before {
          display: none;
        }
      }
    }
  }

  // 错误提示美化
  :global(.van-field__error-message) {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 6px;
    padding-left: 4px;
    display: flex;
    align-items: center;

    &::before {
      content: '⚠';
      margin-right: 4px;
      font-size: 14px;
    }
  }

  // 特殊字段样式
  :global(.van-field) {
    // 电话号码字段
    &[data-field="电话"] {
      .van-field__control {
        font-family: 'Courier New', monospace;
        letter-spacing: 1px;
      }
    }

    // 姓名字段
    &[data-field="姓名"] {
      .van-field__control {
        font-weight: 600;
      }
    }
  }
}

// 弹出选择器美化
:global(.van-popup) {
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);

  .van-picker {
    background: #fff;

    .van-picker__toolbar {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      padding: 16px 20px;

      .van-picker__cancel,
      .van-picker__confirm {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
        transition: opacity 0.3s ease;

        &:active {
          opacity: 0.7;
        }
      }

      .van-picker__title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }
    }

    .van-picker-column {
      .van-picker-column__item {
        color: #2c3e50;
        font-weight: 500;
        padding: 12px 0;
        transition: all 0.3s ease;

        &.van-picker-column__item--selected {
          color: #2563eb;
          font-weight: 600;
          font-size: 16px;
          background: rgba(37, 99, 235, 0.05);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .form-container {
    :global(.van-cell) {
      padding: 12px 16px;
    }

    :global(.van-field__label) {
      min-width: 70px;
      font-size: 13px;
    }

    :global(.van-radio-group) {
      gap: 12px;

      .van-radio {
        padding: 6px 8px;

        .van-radio__label {
          font-size: 13px;
        }
      }
    }

    .submit-button-container {
      padding: 20px 12px 12px;

      :global(.van-button) {
        height: 44px;
        font-size: 15px;
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-container {
  animation: fadeInUp 0.5s ease-out;
}
</style>
